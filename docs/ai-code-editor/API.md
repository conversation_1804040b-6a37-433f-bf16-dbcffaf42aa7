# AI Code Editor API Documentation

## Overview

The AI Code Editor API provides endpoints for AI-powered code operations including completion, analysis, refactoring, and assistant interactions.

## Base URL

```
/api/ai-code
```

## Authentication

All endpoints support optional user identification through request body parameters:
- `userId`: Optional user identifier
- `workspaceId`: Optional workspace identifier

## Rate Limiting

- **Per User**: 60 requests per minute, 1000 requests per hour
- **Global**: Configurable based on AI service quotas
- **Headers**: Rate limit information is returned in response headers

## Error Handling

All endpoints return errors in a consistent format:

```typescript
{
  success: false,
  error: {
    message: string,
    code: string,
    details?: any
  }
}
```

### Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Invalid request data |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `AI_SERVICE_ERROR` | AI service unavailable |
| `CONTEXT_TOO_LONG` | Input exceeds maximum length |
| `QUOTA_EXCEEDED` | API quota exceeded |

## Endpoints

### Code Completion

#### POST /api/ai-code/completion

Generate AI-powered code completions.

**Request Body:**
```typescript
{
  code: string;                    // Source code context
  position: {                      // Cursor position
    line: number;                  // 1-based line number
    column: number;                // 0-based column number
  };
  language: string;                // Programming language
  context?: {                      // Optional context
    fileName?: string;             // File name
    projectContext?: string;       // Project description
    recentChanges?: string[];      // Recent code changes
  };
  userId?: string;                 // User identifier
  workspaceId?: string;            // Workspace identifier
}
```

**Response:**
```typescript
{
  success: true,
  data: {
    suggestions: Array<{
      id: string;                  // Unique suggestion ID
      text: string;                // Display text
      insertText: string;          // Text to insert
      confidence: number;          // 0-1 confidence score
      type: 'completion' | 'snippet' | 'import' | 'function' | 'variable';
      description?: string;        // Optional description
      documentation?: string;      // Optional documentation
      priority: number;            // 0-10 priority score
      range: {                     // Replacement range
        start: { line: number; column: number };
        end: { line: number; column: number };
      };
    }>;
    metadata: {
      timestamp: string;
      operation: string;
      duration: number;
    };
    aiMetadata: {
      model: string;
      tokensUsed: number;
      processingTime: number;
      cacheHit: boolean;
    };
  }
}
```

**Example:**
```bash
curl -X POST /api/ai-code/completion \
  -H "Content-Type: application/json" \
  -d '{
    "code": "const user = { name: \"John\", age: 30 };\nuser.",
    "position": { "line": 2, "column": 5 },
    "language": "typescript",
    "context": {
      "fileName": "user.ts"
    }
  }'
```

### Code Analysis

#### POST /api/ai-code/analysis

Analyze code for errors, suggestions, and quality metrics.

**Request Body:**
```typescript
{
  code: string;                    // Source code to analyze
  language: string;                // Programming language
  fileName?: string;               // Optional file name
  includeStyle?: boolean;          // Include style suggestions (default: true)
  includePerformance?: boolean;    // Include performance analysis (default: true)
  includeSecurity?: boolean;       // Include security analysis (default: true)
  userId?: string;                 // User identifier
  workspaceId?: string;            // Workspace identifier
}
```

**Response:**
```typescript
{
  success: true,
  data: {
    errors: Array<{
      id: string;                  // Unique error ID
      message: string;             // Error message
      severity: 'error' | 'warning' | 'info' | 'hint';
      range: {                     // Error location
        start: { line: number; column: number };
        end: { line: number; column: number };
      };
      code?: string;               // Error code
      source: string;              // Analysis source
      fixes?: Array<{              // Suggested fixes
        id: string;
        title: string;
        description: string;
        edits: Array<{
          range: { start: Position; end: Position };
          newText: string;
        }>;
        confidence: number;
      }>;
    }>;
    suggestions: Array<{
      id: string;                  // Unique suggestion ID
      title: string;               // Suggestion title
      description: string;         // Detailed description
      type: 'extract-method' | 'rename' | 'inline' | 'move' | 'optimize' | 'style';
      range: { start: Position; end: Position };
      preview: string;             // Preview of changes
      edits: Array<{
        range: { start: Position; end: Position };
        newText: string;
      }>;
      confidence: number;          // 0-1 confidence score
      impact: 'low' | 'medium' | 'high';
    }>;
    metrics: {
      complexity: number;          // 0-10 complexity score
      maintainability: number;     // 0-10 maintainability score
      readability: number;         // 0-10 readability score
      performance: number;         // 0-10 performance score
      security: number;            // 0-10 security score
      testCoverage?: number;       // 0-100 test coverage percentage
    };
    metadata: {
      aiAnalysis: ResponseMetadata;
      staticAnalysis: ResponseMetadata;
    };
    aiMetadata: AIResponseMetadata;
  }
}
```

#### GET /api/ai-code/analysis/symbols

Parse code symbols and structure.

**Query Parameters:**
- `code`: URL-encoded source code
- `language`: Programming language
- `userId`: Optional user identifier
- `workspaceId`: Optional workspace identifier

**Response:**
```typescript
{
  success: true,
  data: {
    functions: Array<{
      name: string;
      range: { start: Position; end: Position };
      parameters: string[];
    }>;
    classes: Array<{
      name: string;
      range: { start: Position; end: Position };
      methods: string[];
    }>;
    variables: Array<{
      name: string;
      range: { start: Position; end: Position };
      type?: string;
    }>;
    imports: Array<{
      module: string;
      range: { start: Position; end: Position };
      items: string[];
    }>;
  }
}
```

### AI Assistant

#### POST /api/ai-code/assistant

Send a message to the AI coding assistant.

**Request Body:**
```typescript
{
  message: string;                 // User message
  codeContext?: {                  // Optional code context
    file: string;                  // File name
    selection?: {                  // Selected code
      range: { start: Position; end: Position };
      text: string;
    };
    fullCode?: string;             // Full file content
  };
  conversationHistory?: Array<{    // Previous messages
    id: string;
    type: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    codeContext?: any;
  }>;
  userId?: string;                 // User identifier
  workspaceId?: string;            // Workspace identifier
}
```

**Response:**
```typescript
{
  success: true,
  data: {
    message: {
      id: string;                  // Message ID
      type: 'assistant';           // Message type
      content: string;             // Assistant response
      timestamp: string;           // ISO timestamp
      codeContext?: any;           // Code context if applicable
      suggestions?: Array<any>;    // Code suggestions
      actions?: Array<{            // Suggested actions
        id: string;
        type: 'apply-fix' | 'apply-refactoring' | 'insert-code' | 'explain-code' | 'generate-tests';
        title: string;
        description: string;
        data: any;
      }>;
    };
    metadata: ResponseMetadata;
    aiMetadata: AIResponseMetadata;
  }
}
```

### Code Formatting

#### POST /api/ai-code/formatting

Format code with AI-powered suggestions.

**Request Body:**
```typescript
{
  code: string;                    // Source code to format
  language: string;                // Programming language
  options?: {                      // Formatting options
    indentSize?: number;           // Indent size (default: 2)
    indentType?: 'spaces' | 'tabs'; // Indent type (default: 'spaces')
    maxLineLength?: number;        // Max line length (default: 100)
    insertFinalNewline?: boolean;  // Insert final newline (default: true)
    trimTrailingWhitespace?: boolean; // Trim trailing whitespace (default: true)
    semicolons?: boolean;          // Use semicolons (default: true)
    singleQuotes?: boolean;        // Use single quotes (default: true)
    trailingCommas?: boolean;      // Use trailing commas (default: true)
    bracketSpacing?: boolean;      // Bracket spacing (default: true)
    arrowParens?: 'avoid' | 'always'; // Arrow function parens (default: 'avoid')
  };
  userId?: string;                 // User identifier
  workspaceId?: string;            // Workspace identifier
}
```

**Response:**
```typescript
{
  success: true,
  data: {
    formattedCode: string;         // Formatted code
    edits: Array<{                 // Applied edits
      range: { start: Position; end: Position };
      newText: string;
    }>;
    suggestions: string[];         // Formatting suggestions
    metadata: ResponseMetadata;
  }
}
```

#### GET /api/ai-code/formatting/suggestions

Get formatting suggestions without applying changes.

**Query Parameters:**
- `code`: URL-encoded source code
- `language`: Programming language
- `userId`: Optional user identifier
- `workspaceId`: Optional workspace identifier

#### OPTIONS /api/ai-code/formatting/options

Get default formatting options for a language.

**Query Parameters:**
- `language`: Programming language

**Response:**
```typescript
{
  success: true,
  data: {
    language: string;
    defaultOptions: FormattingOptions;
    supportedLanguages: string[];
  }
}
```

### Health Check

#### GET /api/ai-code/health

Check the health status of all AI services.

**Query Parameters:**
- `detailed`: Set to 'true' for detailed health information

**Response:**
```typescript
{
  success: true,
  data: {
    overall: boolean;              // Overall health status
    aiCode: boolean;               // AI code service health
    codeAnalysis: boolean;         // Code analysis service health
    aiModel: boolean;              // AI model service health
    codeFormatting: boolean;       // Code formatting service health
    timestamp: string;             // Check timestamp
    services?: {                   // Detailed info (if requested)
      aiCode: {
        healthy: boolean;
        config: AIServiceConfig;
        metrics: ServiceMetrics;
      };
      // ... other services
    };
  }
}
```

## WebSocket Events (Future)

The API is designed to support WebSocket connections for real-time features:

### Events

- `completion:request` - Request code completion
- `completion:response` - Receive completion suggestions
- `analysis:start` - Start code analysis
- `analysis:progress` - Analysis progress update
- `analysis:complete` - Analysis results
- `assistant:message` - Send/receive assistant messages

## SDK Usage

### JavaScript/TypeScript

```typescript
import { AICodeAPI } from '@/lib/ai-code-api';

const api = new AICodeAPI({
  baseURL: '/api/ai-code',
  userId: 'user-123',
  workspaceId: 'workspace-456',
});

// Get code completions
const completions = await api.getCompletions({
  code: 'const user = { name: "John" };\nuser.',
  position: { line: 2, column: 5 },
  language: 'typescript',
});

// Analyze code
const analysis = await api.analyzeCode({
  code: 'function test() { console.log("hello"); }',
  language: 'javascript',
});

// Chat with assistant
const response = await api.sendMessage({
  message: 'Explain this function',
  codeContext: {
    file: 'test.js',
    selection: { /* ... */ },
  },
});
```

## Rate Limiting Headers

All responses include rate limiting information:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
X-RateLimit-Retry-After: 60
```

## Caching

- **Cache-Control**: Responses include appropriate cache headers
- **ETags**: Conditional requests supported where applicable
- **TTL**: Default cache TTL is 1 hour for AI responses

## Monitoring

Health check endpoint provides service status and metrics:
- Response times
- Success/error rates
- Token usage
- Cache hit rates
- Model performance metrics
